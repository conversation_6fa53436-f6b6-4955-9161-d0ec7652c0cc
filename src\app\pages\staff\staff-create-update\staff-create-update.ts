import { Ng<PERSON>ty<PERSON> } from '@angular/common';
import {
  afterNextRender,
  Component,
  DestroyRef,
  inject,
  Signal,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { StaffService } from '@core/services/http/staff';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { log } from '@core/utils/logger';
import { CustomValidators } from '@core/validators/custom.validator';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { IStaff } from '@models/interfaces/staff';
import { TranslateModule } from '@ngx-translate/core';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputTextareaComponent } from '@shared/inputs/input-textarea/input-textarea';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzModalComponent } from 'ng-zorro-antd/modal';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import {
  NzUploadComponent,
  NzUploadFile,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { delay, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

@Component({
  selector: 'app-staff-create-update',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    NzButtonComponent,
    TranslateModule,
    InputGenericComponent,
    NzSpinComponent,
    NzRowDirective,
    NzColDirective,
    NzFormControlComponent,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzIconDirective,
    NzPopoverDirective,
    NzUploadComponent,
    NzModalComponent,
    InputTextareaComponent,
    NgStyle,
  ],
  providers: [UploadService],
  templateUrl: './staff-create-update.html',
  styleUrl: './staff-create-update.less',
})
export class StaffCreateUpdateComponent extends CreateUpdateItem {
  private route = inject(ActivatedRoute);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private staffService = inject(StaffService);
  private fb = inject(FormBuilder);
  private destroyRef = inject(DestroyRef);
  private breadcrumbService = inject(BreadcrumbService);

  protected baseForm!: FormGroup;
  // Convert to signals
  protected saveButtonTitle = signal<string>('STAFF.saveStaff');
  protected abortButtonTitle = signal<string>('Staff.deleteStaff');
  protected crudMode = signal<crudActionType>(crudActionType.create);
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected staffId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected staff: Signal<IStaff | undefined> = this.staffService.staff$;

  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  // VIEWCHILD
  protected tplButton = viewChild<TemplateRef<any>>('tplButton');

  /**
   * Costruttore del componente staff create/update.
   * Inizializza il form, gestisce i parametri di routing e configura l'header.
   * @returns void
   */
  constructor() {
    super();
    this.initForm();
    this.headerService.setCurrentSection(currentSectionType.staffDetail);
    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          this.staffId.set(<string>params.get('id'));
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.staffDetail,
        this.tplButton(),
      );
    });
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? this.isValidForm.set(false)
      : this.isValidForm.set(true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };
  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLAOD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAEG FILE :IST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  /**
   * Inizializza il form reattivo con i campi e le validazioni per lo staff.
   * Include validazioni per nome, cognome, email, ruolo, descrizione e immagini.
   * @returns void
   */
  private initForm() {
    this.baseForm = this.fb.group({
      id: [
        '',
        [
          Validators.nullValidator,
          Validators.minLength(2),
          Validators.maxLength(200),
        ],
      ],
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(200),
        ],
      ],
      surname: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(200),
        ],
      ],
      email: [
        '',
        [Validators.required, Validators.pattern(CustomValidators.emailRegex)],
      ],
      images: ['', [Validators.required, ValidateImages(1, 5)]],
      role: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(200),
        ],
      ],
      description: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(800),
        ],
      ],
    });
  }

  /**
   * Imposta la modalità CRUD (create o update) in base ai dati di routing.
   * @returns void
   */
  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  /**
   * Configura il componente per la modalità di creazione di un nuovo staff.
   * Resetta il form, imposta i titoli dei pulsanti e mostra il breadcrumb.
   * @returns void
   */
  override setCreateMode() {
    this.baseForm.reset();
    this.initForm();
    this.staffService.setStaff(undefined);
    this.saveButtonTitle.set('STAFF.saveStaff');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di uno staff esistente.
   * Carica i dati dello staff tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.saveButtonTitle.set('STAFF.updateStaff');
    this.abortButtonTitle.set('STAFF.deleteStaff');

    this.staffService.readOne(this.staffId()).subscribe({
      next: (res) => {
        this.fillFormData();
        this.initUploadService();
        const breadcrumbData = res.data?.name + ' ' + res.data?.surname;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/staff');
      },
    });
  }

  fillFormData() {
    FormUtils.fillUpdateDataForm(this.baseForm, this.staff()!);
  }

  ngOnInit(): void {
    this.observeFormChanges();
  }

  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((item) => {
        this.isValidForm.set(false);
      });

    this.baseForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(this.baseForm.valid);
      });
  }

  onAbortClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.router.navigateByUrl('/staff');
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.staffService.delete(this.staffId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/staff');
                this.messageService.addSuccessMessage('STAFF.deleteSuccess');
                log(`STAFF ID: ${this.staffId()} - Eliminato`);
              },
            });
          },
          title: 'STAFF.confirmDeleteTitle',
          subtitle: 'STAFF.confirmDeleteSubtitle',
        });
        break;
    }
  }

  onDataSaveClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }

  /**
   * Gestisce la creazione di un nuovo staff.
   * Invia i dati del form al server tramite HTTP POST.
   * @returns void
   */
  override onDataSubmit() {
    this.loading.set(true);
    const staff = this.baseForm.value;
    FormUtils.removeObjectNullProperties(staff);
    this.messageService.addLoadingMessage('loading');
    this.staffService.create(staff).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('STAFF.createSuccess');
        this.loading.set(false);
        this.router.navigate(['staff', res.data!.id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Gestisce l'aggiornamento di uno staff esistente.
   * Invia i dati modificati al server tramite HTTP PUT e aggiorna il breadcrumb.
   * @returns void
   */
  override onDataUpdate() {
    this.loading.set(true);
    const staff = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');
    this.staffService.update(this.staffId(), staff).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('STAFF.updateSuccess');
        const breadcrumbData = res.data?.name + ' ' + res.data?.surname;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
  }
}
