import { Injectable, TemplateRef, Type, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ModalOptions, NzModalRef, NzModalService } from 'ng-zorro-antd/modal';

export interface InfoModalOptions {
  confirmFn: Function;
  title: string;
  subtitle: string;
  cancelFn?: Function;
  closable?: boolean;
  showCancelButton?: boolean;
}

export interface CustomModalOptions {
  confirmFn: Function;
  title: string | TemplateRef<any>;
  content: TemplateRef<any> | Type<any> | any;
  footer?: TemplateRef<any>;
  cancelFn?: Function;
  width?: string | number;
  height?: string | number;
  closable?: boolean;
  maskClosable?: boolean;
  fullScreen?: boolean;
  className?: string;
  componentData?: any;
}

export interface ConfirmDeleteOptions {
  confirmFn: Function;
  title: string;
  subtitle: string;
  cancelFn?: Function;
}

/**
 * Servizio per la gestione centralizzata dei modali nell'applicazione.
 * Fornisce metodi per mostrare modali di conferma, info, errore, ecc.
 */
@Injectable({
  providedIn: 'root',
})
export class ModalService {
  private nzModalService = inject(NzModalService);
  private translateService = inject(TranslateService);

  private nzModalRef: NzModalRef;

  /**
   * Mostra un modal informativo con titolo, sottotitolo e azioni di conferma/cancellazione.
   * @param confirmFn Funzione da eseguire alla conferma
   * @param title Titolo del modal
   * @param subtitle Sottotitolo del modal
   * @param cancelFn Funzione opzionale da eseguire alla cancellazione
   * @param closable Se il modal è chiudibile
   * @param showCancelButton Se mostrare il pulsante annulla
   * @returns void
   */
  /**
   * Mostra un modal informativo con titolo, sottotitolo e azioni di conferma/cancellazione.
   * @param options Oggetto con tutte le proprietà del modal
   * @returns void
   */
  infoModal(options: InfoModalOptions): void {
    const modalTitle = options.title || '';
    const modalSubtitle = options.subtitle || '';
    const _closable = options.closable ?? true;
    this.nzModalService.info({
      nzTitle: this.translateService.instant(modalTitle),
      nzContent: this.translateService.instant(modalSubtitle),
      nzCancelText: options.showCancelButton
        ? this.translateService.instant('MODAL.abort')
        : null,
      nzCancelDisabled: !options.showCancelButton,
      nzOnOk: () => {
        options.confirmFn != undefined ? options.confirmFn() : '';
      },
      nzOnCancel: () => {
        options.cancelFn != undefined ? options.cancelFn() : '';
      },
      nzClosable: _closable,
      nzMaskClosable: _closable,
    });
  }

  /**
   * Mostra un modal custom con contenuto, titolo, footer e varie opzioni.
   * @param options Oggetto con tutte le proprietà del modal custom
   * @returns void
   */
  customModal(options: CustomModalOptions): void {
    const modalTitle = options.title || '';
    const _content = options.content || '<div></div>';
    const _footer = options.footer || null;
    const _closable = options.closable ?? true;
    const _maskClosable = options.maskClosable ?? true;

    const modalConfig = {
      nzTitle:
        typeof options.title === 'string'
          ? this.translateService.instant(modalTitle as string)
          : options.title,
      nzContent: _content,
      nzWidth: options.fullScreen ? '100vw' : options.width || 520,
      nzHeight: options.height || 'auto',
      nzOnOk: () => {
        options.confirmFn != undefined ? options.confirmFn() : '';
      },
      nzOnCancel: () => {
        options.cancelFn != undefined ? options.cancelFn() : '';
      },
      nzClosable: _closable,
      nzMaskClosable: _maskClosable,
      ...(options.fullScreen && {
        nzWrapClassName: 'full-screen-modal',
        nzStyle: { width: '100vw', height: '100vh', top: 0, padding: 0 },
      }),
      ...(options.className && { nzClassName: options.className }),
    };

    this.nzModalRef = this.nzModalService.create({
      ...modalConfig,
      ...(options.footer ? { nzFooter: _footer } : { nzFooter: null }),
      ...(options.componentData ? { nzData: options.componentData } : {}),
    });
  }

  updateModal(config: Partial<ModalOptions>) {
    if (!this.nzModalRef?.containerInstance) return;
    // Translate nzTitle if exists
    if (config.nzTitle) {
      config = {
        ...config,
        nzTitle: config.nzTitle,
      };
    }
    const prevConfig = this.nzModalRef.containerInstance.config;
    // Update config
    this.nzModalRef?.updateConfig({ ...prevConfig, ...config });
    this.nzModalRef.containerInstance.cdr.markForCheck();
  }

  closeModal() {
    this.nzModalService.closeAll();
  }

  /**
   * Mostra un modal di conferma per eliminazione.
   * @param options Oggetto con tutte le proprietà del modal di conferma
   * @returns void
   */
  confirmDelete(options: ConfirmDeleteOptions): void {
    this.nzModalService.error({
      nzTitle: this.translateService.instant(options.title),
      nzContent: this.translateService.instant(options.subtitle),
      nzCancelText: this.translateService.instant('MODAL.abort'),
      nzOkDanger: true,
      nzOnOk: () => {
        options.confirmFn != undefined ? options.confirmFn() : '';
      },
      nzOnCancel: () => {
        options.cancelFn != undefined ? options.cancelFn() : '';
      },
    });
  }

  confirmDeleteCanDeactivate(
    title: string,
    subtitle: string,
  ): Promise<boolean> {
    return new Promise((resolve) => {
      this.nzModalService.warning({
        nzTitle: this.translateService.instant(title),
        nzContent: this.translateService.instant(subtitle),
        nzCancelText: this.translateService.instant('MODAL.abort'),
        nzOkText: this.translateService.instant('MODAL.yes'),
        nzOkDanger: true,
        nzClosable: false,
        nzOnOk: () => resolve(true),
        nzOnCancel: () => resolve(false),
      });
    });
  }
}
