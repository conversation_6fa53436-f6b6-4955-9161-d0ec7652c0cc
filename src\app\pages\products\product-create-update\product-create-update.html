<!-- MAIN CONTAINER -->
<div nz-row #target class="h-100">
  <div nz-col nzSpan="24" class="h-100">
    <div class="container">
      <div class="form">
        <!-- LOADING SPINNER -->
        <nz-spin [nzSpinning]="loading()">
          <!-- PRODUCT FORM -->
          <form nz-form [formGroup]="baseForm()" nzLayout="vertical">
            <div nz-row [nzGutter]="16">
              <!-- LEFT COLUMN - MAIN PRODUCT INFO -->
              <div nz-col [nzSpan]="13">
                <!-- PRODUCT NAME & VARIANT -->
                <div nz-row [nzGutter]="16">
                  <!-- PRODUCT NAME -->
                  <div nz-col [nzSpan]="16">
                    <app-input-generic
                      [parentForm]="baseForm()"
                      [controlName]="'name'"
                      [label]="'PRODUCTS.productName' | translate"
                      [placeholder]="'PRODUCTS.namePlaceholder' | translate"
                    ></app-input-generic>
                  </div>
                  <!-- VARIANT NAME -->
                  <div nz-col [nzSpan]="8">
                    <app-input-generic
                      [parentForm]="baseForm()"
                      [controlName]="'variantName'"
                      [label]="'PRODUCTS.variantName' | translate"
                      [placeholder]="
                        'PRODUCTS.varianNamePlaceholder' | translate
                      "
                    ></app-input-generic>
                  </div>
                </div>

                <!-- CATEGORIES SECTION -->
                <div nz-row [nzGutter]="24" [nzAlign]="'bottom'">
                  <div nz-col [nzFlex]="'auto'">
                    <app-input-select
                      [parentForm]="baseForm()"
                      [allowClear]="true"
                      [serverSearch]="true"
                      [maxTagCount]="5"
                      [labelPosition]="'top'"
                      [controlName]="'categories'"
                      [label]="'PRODUCTS.category' | translate"
                      [optionList]="categoriesList()"
                      [configKey]="{ label: 'name', value: 'id' }"
                      [mode]="'default'"
                      [placeholder]="
                        'PRODUCTS.categoriesPlaceholder' | translate
                      "
                    ></app-input-select>
                  </div>
                  <!-- ADD CATEGORY BUTTON -->
                  <div nz-col [nzFlex]="'45px'">
                    <app-simple-button
                      [autoMinify]="false"
                      [iconOnly]="true"
                      [icon]="'plus'"
                      (onButtonClick)="openNewCategorModal()"
                    ></app-simple-button>
                  </div>

                  <div nz-col [nzSpan]="24" style="margin-top: 1.3rem">
                    <!-- PRODUCT DESCRIPTION -->
                    <app-input-textarea
                      [parentForm]="baseForm()"
                      [controlName]="'description'"
                      [size]="{ minRows: 6, maxRows: 12 }"
                      [label]="'PRODUCTS.description' | translate"
                      [placeholder]="
                        'PRODUCTS.descriptionPlaceholder' | translate
                      "
                    ></app-input-textarea>
                  </div>
                </div>

                <!-- EXTRA INFORMATION SECTION -->
                <div nz-row>
                  <!-- EXTRA INFO TOGGLE -->
                  <div nz-row style="margin-bottom: 1.3rem">
                    <app-toggle-checkbox
                      [label]="'PRODUCTS.additionalInfo' | translate"
                      [checked]="showExtraInfo()"
                      (onCheckboxChange)="onShowExtraInfoChange()"
                    ></app-toggle-checkbox>
                  </div>

                  <!-- EXTRA INFO FIELDS (DYNAMIC) -->
                  @if (showExtraInfo()) {
                    @for (option of extraInfos.controls; track $index) {
                      <div
                        nz-row
                        [nzGutter]="16"
                        class="w-100"
                        [nzAlign]="'middle'"
                      >
                        <!-- PROPERTY NAME -->
                        <div nz-col [nzSpan]="11">
                          <app-input-generic
                            [parentForm]="option"
                            [controlName]="'key'"
                            [label]="'PRODUCTS.propertyName' | translate"
                            [placeholder]="
                              'PRODUCTS.propertyNamePlaceholder' | translate
                            "
                          ></app-input-generic>
                        </div>
                        <!-- PROPERTY VALUE -->
                        <div nz-col [nzSpan]="11">
                          <app-input-generic
                            [parentForm]="option"
                            [controlName]="'value'"
                            [label]="'PRODUCTS.propertyValue' | translate"
                            [placeholder]="
                              'PRODUCTS.propertyValuePlaceholder' | translate
                            "
                          ></app-input-generic>
                        </div>
                        <!-- DELETE BUTTON -->
                        <div nz-col [nzSpan]="2" style="margin-top: 0.8rem">
                          <a
                            nz-popconfirm
                            nzPopconfirmTitle="Sicuro di voler cancellare?"
                            nzPopconfirmPlacement="bottom"
                            (nzOnConfirm)="removeFormExtraInfo($index)"
                            nzOkType="danger"
                            (click)="$event.stopPropagation()"
                            class="error-color"
                          >
                            <span nz-icon nzType="client-ui:delete"></span>
                          </a>
                        </div>
                      </div>
                    }
                  }
                  <!-- ADD NEW PROPERTY BUTTON -->
                  @if (showExtraInfo()) {
                    <div nz-row [nzGutter]="16" class="w-100">
                      <div nz-col [nzSpan]="12"></div>
                      <div
                        nz-col
                        [nzSpan]="12"
                        style="display: flex; justify-content: flex-end"
                      >
                        <app-simple-button
                          [autoMinify]="false"
                          [title]="'PRODUCTS.addProperty' | translate"
                          [icon]="'plus'"
                          (onButtonClick)="addFormExtraInfo(extraInfos)"
                        ></app-simple-button>
                      </div>
                    </div>
                  }
                </div>
                <!-- LINK TO SHOP INPUT -->
              </div>

              <!-- VERTICAL DIVIDER -->
              <div nz-col [nzSpan]="1" [nzFlex]="'center'">
                <nz-divider
                  nzType="vertical"
                  style="height: 100%; text-align: center"
                ></nz-divider>
              </div>

              <!-- RIGHT COLUMN - PRODUCT SETTINGS -->
              <div nz-col [nzSpan]="10">
                <!-- PRODUCT STATUS SECTION -->
                <div nz-row [nzGutter]="8">
                  <div nz-col [nzSpan]="24" [nzXXl]="6">
                    <app-input-checkbox
                      [parentForm]="baseForm()"
                      [controlName]="'isOnline'"
                      [label]="'PRODUCTS.productStatus' | translate"
                      [optionList]="showProductStatus()"
                      [name]="'PRODUCTS.showOnShop' | translate"
                    >
                    </app-input-checkbox>
                  </div>
                  <div nz-col [nzSpan]="24" [nzXXl]="18">
                    <app-input-generic
                      [label]="'PRODUCTS.linkToShop' | translate"
                      [parentForm]="baseForm()"
                      [controlName]="'linkToShop'"
                      [placeholder]="
                        'PRODUCTS.linkToShopPlaceholder' | translate
                      "
                    ></app-input-generic>
                  </div>
                </div>

                <!-- PRODUCT IMAGES SECTION -->
                <nz-form-item
                  [formGroup]="baseForm()"
                  style="margin-bottom: 1.3rem"
                >
                  <nz-form-control>
                    <div class="clearfix">
                      <!-- IMAGE UPLOAD LABEL WITH INFO -->
                      <nz-form-label [nzRequired]="false">
                        <i
                          nz-icon
                          class="client-ui:info-icon"
                          nz-popover
                          [nzPopoverTitle]="'limits.limit' | translate"
                          [nzPopoverContent]="tplImageInfo"
                          nzType="info-circle"
                          class="mr-4"
                        ></i>
                        <strong>{{ "images" | translate }}</strong>
                        <!-- IMAGE LIMITS INFO TEMPLATE -->
                        <ng-template #tplImageInfo>
                          <div class="info-image">
                            <strong>{{ "limits.width" | translate }}: </strong>
                            <div class="min-max">
                              <span>min {{ Limits.width.min }}px</span>
                              <span>max {{ Limits.width.max }}px</span>
                            </div>
                            <strong>{{ "limits.heigth" | translate }}: </strong>
                            <div class="min-max">
                              <span>min {{ Limits.height.min }}px</span>
                              <span>max {{ Limits.height.max }}px</span>
                            </div>
                          </div>
                        </ng-template>
                      </nz-form-label>
                      <!-- IMAGE UPLOAD COMPONENT -->
                      <nz-upload
                        [nzAction]="'http://localhost:4201/api/fakeImage'"
                        [nzCustomRequest]="uploadRequest"
                        [(nzFileList)]="FileList"
                        [nzShowButton]="FileList.length < 5"
                        [nzFileType]="
                          'image/png,image/jpeg,image/gif,image/bmp,image/jpg,image/webp'
                        "
                        [nzHeaders]="setMediaUploadHeaders"
                        [nzPreview]="handlePreview"
                        [nzRemove]="removeItem"
                        [nzShowUploadList]="showUploadList"
                        nzListType="picture-card"
                      >
                        <div>
                          <i nz-icon nzType="client-ui:plus"></i>
                          <div>{{ "upload" | translate }}</div>
                        </div>
                      </nz-upload>
                      <!-- IMAGE PREVIEW MODAL -->
                      <nz-modal
                        [nzVisible]="previewVisible()"
                        [nzContent]="modalContent"
                        [nzFooter]="null"
                        (nzOnCancel)="previewVisible.set(false)"
                      >
                        <ng-template #modalContent>
                          <img
                            [src]="previewImage()"
                            [ngStyle]="{ width: '100%' }"
                          />
                        </ng-template>
                      </nz-modal>
                    </div>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div></form
        ></nz-spin>
      </div>
    </div>
  </div>
</div>

<!-- ACTION BUTTONS TEMPLATE -->
<ng-template #tplButton>
  <!-- SAVE/UPDATE BUTTON -->
  <button
    nz-button
    nzType="primary"
    (click)="onDataSaveClick()"
    [disabled]="!isValidForm() || loading()"
  >
    {{ saveButtonTitle() | translate }}
    @if (crudMode() === crudActionType().create) {
      <i nz-icon nzType="client-ui:plus"></i>
    } @else {
      <i nz-icon nzType="save"></i>
    }
  </button>
</ng-template>
