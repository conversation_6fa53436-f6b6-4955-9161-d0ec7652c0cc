import {
  Component,
  inject,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { log } from '@core/utils/logger';
import { currentSectionType } from '@models/enums/current-section';
import { IImage } from '@models/interfaces/image';
import { TranslateModule } from '@ngx-translate/core';
import { ITableRowAction } from '@shared/table/types/table.action';
import { ITableColumn } from '@shared/table/types/table.column';
import { ITableQuery } from '@shared/table/types/table.query';
import { IRefreshData } from '@shared/table/types/table.refresh-data';
import { ITableSetting } from '@shared/table/types/table.settings';

import { trackEvent } from '@aptabase/web';
import { EllipsisDirective } from '@core/directives/ellipsis';
import { DateFormatPipe } from '@core/pipes/date.pipe';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { StaffService } from '@core/services/http/staff';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { IStaff } from '@models/interfaces/staff';
import { TableComponent } from '@shared/table/table';
import { TableQueryService } from '@shared/table/table-query.service';
import {
  NzAvatarComponent,
  NzAvatarGroupComponent,
} from 'ng-zorro-antd/avatar';
import { NzImageModule, NzImageService } from 'ng-zorro-antd/image';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzTypographyModule } from 'ng-zorro-antd/typography';

@Component({
  selector: 'app-staff-list',
  standalone: true,
  imports: [
    TableComponent,
    DateFormatPipe,
    NullValuePipe,
    EllipsisDirective,
    NzImageModule,
    NzAvatarComponent,
    NzAvatarGroupComponent,
    NzToolTipModule,
    NzTypographyModule,
    TranslateModule,
  ],
  providers: [TableQueryService],
  templateUrl: './staff-list.html',
  styleUrl: './staff-list.less',
})
export class StaffListComponent {
  // SERVICES
  private headerService = inject(HeaderService);
  private staffService = inject(StaffService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private messageService = inject(MessageService);
  private imageService = inject(NzImageService);

  tplId = viewChild<TemplateRef<any>>('tplId');
  tplImage = viewChild<TemplateRef<any>>('tplImage');
  // Convert to signals
  readonly loading = signal<boolean>(false);
  readonly data = signal<any[]>([]);
  readonly dataQuery = signal<any>(null);
  readonly tableLayoutSettings = signal<ITableSetting>(<ITableSetting>{});
  readonly tableQueryRequest = signal<ITableQuery | null>(null);
  readonly refreshData = signal<IRefreshData>({
    interval: 0,
    min: 0,
    max: 180,
    step: 30,
  });

  constructor() {
    trackEvent('staff_list_page');
    this.tableLayoutSettings.set({
      singleRowActions: this.buildRowActions(),
      listOfColumns: this.buildTableColumns(),
      dynamicTableHeightOffset: 375,
      pagination: {
        total: 0,
        pageSize: 50,
        pageIndex: 1,
      },
    });
    this.data.set([]);
    this.headerService.setCurrentSection(currentSectionType.staffList);
  }

  onQueryChange(query: ITableQuery) {
    this.tableQueryRequest.set(query);
    this.loading.set(true);

    this.staffService.search(query.meta, query.filter).subscribe((value) => {
      this.data.set(value.data);

      const currentSettings = this.tableLayoutSettings();
      currentSettings.pagination.total = value.meta!.page.total;
      currentSettings.pagination.pageIndex = value.meta!.page.pageIndex;
      currentSettings.pagination.pageSize = value.meta!.page.pageSize;
      this.tableLayoutSettings.set({ ...currentSettings });
      this.loading.set(false);
    });
  }

  ngAfterViewInit(): void {
    this.tableLayoutSettings().listOfColumns.forEach(
      (col) =>
        (col.cellTemplate = col.cellTemplateName
          ? this[col.cellTemplateName]()
          : null),
    );
  }

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.modify',
        icon: 'edit',
        callbackFn: (row: IStaff) => {
          console.log('ROW', row);
          this.router.navigate([row.id], { relativeTo: this.route });
        },
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        callbackFn: (row: IStaff) => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.staffService.delete(row.id).subscribe({
                next: () => {
                  this.onQueryChange(this.tableQueryRequest()!);
                  this.messageService.addSuccessMessage('STAFF.deleteSuccess');
                  log(`STAFF ID: ${row.id} - Eliminato`);
                },
              });
            },
            title: 'STAFF.confirmDeleteTitle',
            subtitle: 'STAFF.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  private buildTableColumns(): ITableColumn[] {
    return [
      {
        id: 'id',
        cellTemplate: this.tplId(),
        cellTemplateName: 'tplId',
        title: 'ID',
        sortOrder: null,
        sortFn: true,
        width: '50px',
        lockOnLeft: true,
        visible: true,
      },
      {
        id: 'images',
        cellTemplate: this.tplImage(),
        cellTemplateName: 'tplImage',
        title: 'STAFF.image',
        width: '50px',
        visible: true,
      },
      {
        id: 'name',
        title: 'STAFF.name',
        sortOrder: null,
        sortFn: true,
        width: '100px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'surname',
        title: 'STAFF.surname',
        sortOrder: null,
        sortFn: true,
        width: '100px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'email',
        title: 'STAFF.email',
        sortOrder: null,
        sortFn: true,
        width: '150px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'role',
        title: 'STAFF.role',
        sortOrder: null,
        sortFn: true,
        width: '100px',
        visible: true,
        hasSearchFilter: true,
      },
    ];
  }

  goToDetail(categoryId: string) {
    this.router.navigate([categoryId], { relativeTo: this.route });
  }

  onImageClick(images: IImage[]): void {
    const imagePreview = images.map((image) => {
      return { src: image.url, alt: image.name };
    });
    this.imageService.preview(imagePreview, { nzZoom: 1, nzRotate: 0 });
  }
}
