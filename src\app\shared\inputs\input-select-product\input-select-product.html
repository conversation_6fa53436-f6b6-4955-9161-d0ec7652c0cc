<nz-form-item [formGroup]="parentForm()" [style]="getCombinedStyles()">
  @if (!!label()) {
    <nz-form-label [nzRequired]="isRequired()" nzNoColon="true">
      {{ label() }} {{ labelSuffix() ? labelSuffix() : null }}
    </nz-form-label>
  }
  <nz-form-control>
    <nz-select
      [formControlName]="controlName()"
      [nzPlaceHolder]="placeholder()"
      [nzMode]="mode()"
      [nzSize]="size()"
      [nzMaxTagCount]="maxTagCount()"
      [nzAllowClear]="allowClear()"
      [nzDisabled]="disabled()"
      [nzShowSearch]="showSearch()"
      [nzServerSearch]="serverSearch()"
      [nzOptionHeightPx]="optionHeightPx()"
      [nzNotFoundContent]="notFound()"
      [compareWith]="compareFn"
      [nzLoading]="isLoading()"
      [nzDropdownStyle]="showDropdown()"
      (nzOnSearch)="onSearchClick($event)"
    >
      @for (option of optionList(); track $index) {
        @if (!isLoading()) {
          <nz-option
            [nzLabel]="option[configKey().label]"
            [nzValue]="option"
            [nzCustomContent]="true"
          >
            <img
              [src]="option.images[0].url"
              [alt]="option.name"
              [width]="imageWidth()"
              [height]="imageHeight()"
              class="product-image"
            />
            <span class="option-label">{{ option[configKey().label] }}</span>
          </nz-option>
        }
      }
      @if (isLoading()) {
        <nz-option nzDisabled nzCustomContent>
          <span nz-icon nzType="loading" class="loading-icon"></span>
        </nz-option>
      }
    </nz-select>
  </nz-form-control>
</nz-form-item>
