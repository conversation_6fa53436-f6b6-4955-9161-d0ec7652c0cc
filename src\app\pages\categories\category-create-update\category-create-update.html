<!-- MAIN CONTAINER -->
<div class="container">
  <!-- CATEGORY FORM -->
  <form nz-form class="form" [formGroup]="baseForm" nzLayout="vertical">
    <!-- LOADING SPINNER -->
    <nz-spin [nzSpinning]="loading()">
      <!-- CATEGORY INFO SECTION -->
      <div nz-row class="w-100" [nzGutter]="16">
        <div nz-col [nzSpan]="12">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <!-- CATEGORY NAME -->
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'name'"
                [label]="'CATEGORIES.categoryName' | translate"
                [placeholder]="'CATEGORIES.categoryNamePlaceholder' | translate"
              ></app-input-generic>
            </div>
            <div nz-col [nzSpan]="24">
              <!-- CATEGORY DESCRIPTION -->
              <app-input-textarea
                [parentForm]="baseForm"
                [controlName]="'description'"
                [label]="'CATEGORIES.description' | translate"
                [placeholder]="'CATEGORIES.descriptionPlaceholder' | translate"
                [size]="{ minRows: 3, maxRows: 15 }"
              ></app-input-textarea>
            </div>
          </div>
        </div>
        <div nz-col [nzSpan]="12">
          <!-- CATEGORY IMAGE UPLOAD -->
          <nz-form-item [formGroup]="baseForm">
            <nz-form-control>
              <div class="clearfix">
                <!-- IMAGE UPLOAD LABEL WITH INFO -->
                <nz-form-label [nzRequired]="false">
                  <i
                    nz-icon
                    class="info-icon"
                    nz-popover
                    [nzPopoverTitle]="'limits.limit' | translate"
                    [nzPopoverContent]="tplImageInfo"
                    nzType="info-circle"
                  ></i>
                  <strong class="info-image">{{ "image" | translate }}</strong>
                  <!-- IMAGE LIMITS INFO TEMPLATE -->
                  <ng-template #tplImageInfo>
                    <div class="info-image">
                      <strong>{{ "limits.width" | translate }}: </strong>
                      <div class="min-max">
                        <span>min {{ Limits.width.min }}px</span>
                        <span>max {{ Limits.width.max }}px</span>
                      </div>
                      <strong>{{ "limits.heigth" | translate }}: </strong>
                      <div class="min-max">
                        <span>min {{ Limits.height.min }}px</span>
                        <span>max {{ Limits.height.max }}px</span>
                      </div>
                    </div>
                  </ng-template>
                </nz-form-label>
                <!-- IMAGE UPLOAD COMPONENT -->
                <nz-upload
                  [nzAction]="'http://localhost:4201/api/fakeImage'"
                  [nzCustomRequest]="uploadRequest"
                  [(nzFileList)]="FileList"
                  [nzShowButton]="FileList.length < 1"
                  [nzFileType]="
                    'image/png,image/jpeg,image/gif,image/bmp,image/jpg,image/webp'
                  "
                  [nzHeaders]="setMediaUploadHeaders"
                  [nzPreview]="handlePreview"
                  [nzRemove]="removeItem"
                  [nzShowUploadList]="showUploadList"
                  nzListType="picture-card"
                >
                  <div>
                    <i nz-icon nzType="client-ui:plus"></i>
                    <div>{{ "upload" | translate }}</div>
                  </div>
                </nz-upload>
                <!-- IMAGE PREVIEW MODAL -->
                <nz-modal
                  [nzVisible]="previewVisible"
                  [nzContent]="modalContent"
                  [nzFooter]="null"
                  (nzOnCancel)="previewVisible = false"
                >
                  <ng-template #modalContent>
                    <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
                  </ng-template>
                </nz-modal>
              </div>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </nz-spin>
  </form>
</div>

<!-- ACTION BUTTONS TEMPLATE -->
<ng-template #tplButton>
  <!-- SAVE/UPDATE BUTTON -->
  <button
    nz-button
    nzType="primary"
    (click)="onDataSaveClick()"
    [disabled]="!isValidForm() || loading()"
  >
    {{ saveButtonTitle() | translate }}
    @if (crudMode() === crudActionType.create) {
      <i nz-icon nzType="client-ui:plus"></i>
    } @else {
      <i nz-icon nzType="save"></i>
    }
  </button>
</ng-template>
