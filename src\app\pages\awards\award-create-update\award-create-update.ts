import {
  afterNextRender,
  Component,
  DestroyRef,
  inject,
  Signal,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AwardsService } from '@core/services/http/awards';
import { ProductsService } from '@core/services/http/products';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { roleType } from '@models/enums/role';
import { IAward, IRecognition } from '@models/interfaces/award';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { IImage } from '@models/interfaces/image';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputSelectProductComponent } from '@shared/inputs/input-select-product/input-select-product';
import { log } from 'ng-zorro-antd/core/logger';
import { NzFormLabelComponent } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzModalComponent } from 'ng-zorro-antd/modal';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import {
  NzUploadFile,
  NzUploadModule,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { delay, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};
@Component({
  selector: 'app-award-create-update',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    InputGenericComponent,
    NzSpinComponent,
    NzGridModule,
    TranslateModule,
    NzUploadModule,
    NzFormLabelComponent,
    NzPopoverDirective,
    NzModalComponent,
    NzIconDirective,
    SimpleButtonComponent,
    InputSelectProductComponent,
    NzSpaceComponent,
    NzSpaceItemDirective,
  ],
  providers: [UploadService],
  templateUrl: './award-create-update.html',
  styleUrl: './award-create-update.less',
})
export class AwardCreateUpdate extends CreateUpdateItem {
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private awardService = inject(AwardsService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private fb = inject(FormBuilder);
  private productsService = inject(ProductsService);
  private breadcrumbService = inject(BreadcrumbService);

  protected baseForm!: FormGroup;
  protected crudMode = signal<crudActionType>(crudActionType.create);
  protected saveButtonTitle = signal<string>('AWARDS.saveAward');
  protected abortButtonTitle = signal<string>('AWARDS.deleteAward');
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected awardId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected award: Signal<IAward | undefined> = this.awardService.award$;

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  protected productsList = signal<
    { label: string; value: string; images: IImage[] }[]
  >([]);

  protected isLoadingProductList = signal<boolean>(false);

  // VIEWCHILD
  protected tplButton = viewChild<TemplateRef<any>>('tplButton');

  // ENUMS
  roleType = roleType;

  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  constructor() {
    super();
    this.initForm();
    this.headerService.setCurrentSection(currentSectionType.awardDetail);
    this.route.paramMap
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        switchMap((params) => {
          this.awardId.set(<string>params.get('id'));
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.awardDetail,
        this.tplButton(),
      );
    });
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  public get awardsArray(): FormArray {
    return this.baseForm.get('awards') as FormArray;
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? this.isValidForm.set(false)
      : this.isValidForm.set(true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };
  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLAOD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAEG FILE :IST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  private initForm() {
    this.baseForm = this.fb.group({
      id: ['', [Validators.nullValidator]],
      year: [
        '',
        [Validators.required, Validators.minLength(4), Validators.maxLength(4)],
      ],
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(200),
        ],
      ],
      images: ['', [Validators.required, ValidateImages(1, 1)]],
      awards: this.fb.array([]),
    });
  }

  observeFormChanges() {
    this.baseForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        log('FORM VALUE CHANGES', value);
        this.isValidForm.set(this.baseForm.valid);
      });
  }

  private fillFormData() {
    // Set basic form fields
    this.baseForm.get('id')?.setValue(this.award()!.id);
    this.baseForm.get('year')?.setValue(this.award()!.year);
    this.baseForm.get('name')?.setValue(this.award()!.name);
    this.baseForm.get('images')?.setValue(this.award()!.images);

    // Clear existing awards array
    this.awardsArray.clear();

    // Create form controls for each award and set values
    this.award()!.awards.forEach((recognition: IRecognition) => {
      // Create new form group for this award
      const awardFormGroup = this.createAward();

      // Set the product value - need to find the matching product from productsList
      const productOption = this.productsList().find(
        (p) => p.value === recognition.product.id,
      );
      if (productOption) {
        awardFormGroup.get('product')?.setValue(productOption);
      }

      // Set the recognition value
      awardFormGroup.get('recognition')?.setValue(recognition.recognition);

      // Add the form group to the awards array
      this.awardsArray.push(awardFormGroup);
    });
  }

  override setCreateMode() {
    this.baseForm.reset();
    this.initForm();
    this.addAward();
    this.awardService.setAward(undefined);
    this.saveButtonTitle.set('AWARDS.saveAward');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di un premio esistente.
   * Carica i dati della categoria tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.saveButtonTitle.set('AWARDS.updateAward');
    this.abortButtonTitle.set('AWARDS.deleteAward');
    this.awardService.readOne(this.awardId()).subscribe({
      next: (res) => {
        const products = res.data!.awards.map((award: IRecognition) => {
          return {
            label: award.product.name,
            value: award.product.id,
            images: award.product.images,
          };
        });
        this.productsList.set(products);
        this.fillFormData();
        this.initUploadService();
        const breadcrumbData = res.data?.name + ' (' + res.data?.year + ')';
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/awards');
      },
    });
  }

  ngOnInit(): void {
    this.observeFormChanges();
  }
  /**
   * Gestisce il click del pulsante di salvataggio.
   * Determina se eseguire creazione o aggiornamento in base alla modalità CRUD.
   * @returns void
   */
  onDataSaveClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }
  override onDataSubmit() {
    this.loading.set(true);
    let award = this.baseForm.value;
    console.log('award', award);

    // Transform awards data to match server expectations
    award.awards = award.awards.map((awardItem: any) => {
      return {
        product: awardItem.product.value,
        recognition: awardItem.recognition,
      };
    });

    FormUtils.removeObjectNullProperties(award);
    this.messageService.addLoadingMessage('loading');
    this.awardService.create(award).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('AWARDS.createSuccess');
        this.loading.set(false);
        this.router.navigate(['awards', res.data!.id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  onDataUpdate() {
    this.loading.set(true);
    let award = this.baseForm.value;

    // Transform awards data to match server expectations
    award.awards = award.awards.map((awardItem: any) => {
      return {
        product: awardItem.product.value,
        recognition: awardItem.recognition,
      };
    });

    FormUtils.removeObjectNullProperties(award);
    this.messageService.addLoadingMessage('loading');
    this.awardService.update(this.awardId(), award).subscribe({
      next: () => {
        this.messageService.addSuccessMessage('AWARDS.updateSuccess');
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  onAbortClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.loading.set(false);
            this.router.navigateByUrl('/awards');
          },
          title: 'AWARDS.confirmDeleteTitle',
          subtitle: 'AWARDS.confirmDeleteSubtitle',
        });
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.awardService.delete(this.awardId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/awards');
                this.messageService.addSuccessMessage('AWARDS.deleteSuccess');
              },
            });
          },
          title: 'AWARDS.confirmDeleteTitle',
          subtitle: 'AWARDS.confirmDeleteSubtitle',
        });
        break;
    }
  }

  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }
  addAward(): void {
    this.awardsArray.push(this.createAward());
  }
  createAward(): FormGroup {
    return this.fb.group({
      product: [null, [Validators.required]],
      recognition: [null, [Validators.required]],
    });
  }

  removeAward(index: number): void {
    FormUtils.smartRemoveFormArray(this.awardsArray, index);
  }

  onProductChange(event: string) {
    this.productsService.readProductsByName(event).subscribe({
      next: (res) => {
        this.awardsArray.controls.forEach((award) => {
          award.get('product')?.setValue(res.data!);
        });
      },
    });
  }

  searchProduct(event: string) {
    this.isLoadingProductList.set(true);
    this.productsService.readProductsByName(event).subscribe({
      next: (res) => {
        const products = res.data!.map((product) => {
          return {
            label: product.name,
            value: product.id,
            images: product.images,
          };
        });
        log('PRODUCTS', products);
        this.productsList.set(products);
        this.isLoadingProductList.set(false);
      },
    });
  }
}
