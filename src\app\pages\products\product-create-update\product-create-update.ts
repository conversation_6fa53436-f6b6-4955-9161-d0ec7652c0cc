import { NgStyle } from '@angular/common';
import {
  afterNextRender,
  Component,
  DestroyRef,
  inject,
  Signal,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CategoriesService } from '@core/services/http/categories';
import { ProductsService } from '@core/services/http/products';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { log } from '@core/utils/logger';
import { CustomValidators } from '@core/validators/custom.validator';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { ICategory } from '@models/interfaces/category';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { IProduct } from '@models/interfaces/product';
import { TranslateModule } from '@ngx-translate/core';
import { CategoryCreateUpdateComponent } from '@pages/categories/category-create-update/category-create-update';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputCheckboxComponent } from '@shared/inputs/input-checkbox/input-checkbox';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputSelectComponent } from '@shared/inputs/input-select/input-select';
import { InputTextareaComponent } from '@shared/inputs/input-textarea/input-textarea';
import { ToggleCheckboxComponent } from '@shared/toggles/toggle-checkbox/toggle-checkbox';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPopconfirmDirective } from 'ng-zorro-antd/popconfirm';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import {
  NzUploadFile,
  NzUploadModule,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { delay, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

@Component({
  selector: 'app-product-create-update',
  standalone: true,
  imports: [
    TranslateModule,
    NzRowDirective,
    NzColDirective,
    InputCheckboxComponent,
    NzFormDirective,
    FormsModule,
    ReactiveFormsModule,
    InputTextareaComponent,
    InputGenericComponent,
    InputSelectComponent,
    NzUploadModule,
    NzInputNumberModule,
    NgStyle,
    NzModalModule,
    TranslateModule,
    NzButtonComponent,
    NzIconDirective,
    NzFormControlComponent,
    NzPopoverDirective,
    NzFormLabelComponent,
    NzRowDirective,
    NzColDirective,
    NzDividerComponent,
    SimpleButtonComponent,
    NzPopconfirmDirective,
    ToggleCheckboxComponent,
    NzSpinComponent,
  ],
  providers: [UploadService],
  templateUrl: './product-create-update.html',
  styleUrl: './product-create-update.less',
})
export class ProductCreateUpdateComponent extends CreateUpdateItem {
  // SERVICES
  private productService = inject(ProductsService);
  private route = inject(ActivatedRoute);
  private headerService = inject(HeaderService);
  private breadcrumbService = inject(BreadcrumbService);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private categoriesService = inject(CategoriesService);
  private destroy$ = inject(DestroyRef);

  // SIGNALS
  protected baseForm = signal<FormGroup>(this.fb.group({}));
  protected crudMode = signal<crudActionType>(crudActionType.create);
  protected productId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected isValidForm = signal<boolean>(false);
  protected showExtraInfo = signal<boolean>(false);
  protected saveButtonTitle = signal<string>('PRODUCTS.saveProduct');
  protected abortButtonTitle = signal<string>('PRODUCTS.deleteProduct');
  protected previewImage = signal<string | undefined>('');
  protected previewVisible = signal<boolean>(false);

  protected categoriesList = this.categoriesService.categories$;

  // CONSTANT SIGNALS
  protected crudActionType = signal(crudActionType);
  protected showProductStatus = signal([
    { label: 'PRODUCTS.showOnShop', value: 'online', checked: true },
  ]);

  protected product: Signal<IProduct> = this.productService.product$;

  // VIEWCHILD
  protected tplButton = viewChild<TemplateRef<any>>('tplButton');
  protected tplCategoryModalFooter = viewChild<TemplateRef<any>>(
    'categoryModalFooter',
  );

  /**
   * Costruttore del componente product create/update.
   * Inizializza il form, gestisce i parametri di routing e configura l'header.
   * @returns void
   */
  constructor() {
    super();
    this.headerService.setCurrentSection(currentSectionType.productDetail);
    this.initForm();
    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          this.productId.set(<string>params.get('id'));
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.productDetail,
        this.tplButton(),
      );
    });
  }

  /**
   * Hook di inizializzazione del componente.
   * Avvia l'osservazione dei cambiamenti del form.
   * @returns void
   */
  ngOnInit(): void {
    this.observeFormChanges();
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm().get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  /**
   * Inizializza il form reattivo con i campi e le validazioni per il prodotto.
   * Include validazioni per nome, link, immagini e informazioni extra.
   * @returns void
   */
  initForm() {
    this.baseForm.set(
      this.fb.group({
        id: [null, [Validators.nullValidator]],
        name: ['', [Validators.required]],
        variantName: ['', [Validators.required]],
        linkToShop: [
          '',
          [
            Validators.nullValidator,
            Validators.pattern(CustomValidators.urlRegex),
          ],
        ],
        description: ['', [Validators.required]],
        categories: [null, [Validators.required]],
        isOnline: [true, [Validators.required]],
        images: ['', [Validators.required, ValidateImages(1, 5)]],
        extraInfos: this.fb.array([]),
      }),
    );
  }

  get extraInfos() {
    return this.baseForm()?.get('extraInfos') as FormArray;
  }

  get categories() {
    return this.baseForm()?.get('categories') as AbstractControl;
  }

  get linkToShop() {
    return this.baseForm()?.get('linkToShop') as AbstractControl;
  }

  /**
   * Crea un nuovo FormGroup per le informazioni extra del prodotto.
   * @param value Valore opzionale per pre-popolare il form
   * @returns FormGroup con campi key e value
   */
  newFormExtraInfo(value?: any) {
    return this.fb.group({
      key: [value?.key || '', [Validators.required]],
      value: [value?.value || '', [Validators.required]],
    });
  }

  /**
   * Aggiunge una nuova informazione extra al FormArray.
   * @param form FormArray a cui aggiungere la nuova informazione
   * @returns void
   */
  addFormExtraInfo(form: FormArray) {
    form.push(this.newFormExtraInfo());
  }

  /**
   * Rimuove un'informazione extra dal FormArray all'indice specificato.
   * Se non ci sono più informazioni, nasconde la sezione extra info.
   * @param index Indice dell'elemento da rimuovere
   * @returns void
   */
  removeFormExtraInfo(index: number) {
    this.extraInfos.removeAt(index);

    if (this.extraInfos.length <= 0) {
      this.showExtraInfo.set(false);
    }
  }

  /**
   * Gestisce il toggle della visualizzazione delle informazioni extra.
   * Abilita/disabilita i controlli del form e aggiunge il primo elemento se necessario.
   * @returns void
   */
  onShowExtraInfoChange() {
    if (!this.showExtraInfo() && this.extraInfos.length <= 0) {
      this.extraInfos.push(this.newFormExtraInfo());
    }
    this.showExtraInfo.set(!this.showExtraInfo());

    if (this.showExtraInfo()) {
      this.extraInfos.enable();
    } else {
      this.extraInfos.disable();
    }
  }

  /**
   * Osserva i cambiamenti del form e gestisce gli errori di upload delle immagini.
   * Imposta isValidForm a false quando si verificano errori di upload.
   * @returns void
   */
  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntilDestroyed(this.destroy$))
      .subscribe((item) => {
        this.isValidForm.set(false);
      });

    this.baseForm()
      .valueChanges.pipe(takeUntilDestroyed(this.destroy$))
      .subscribe(() => {
        this.isValidForm.set(this.baseForm().valid);
      });
  }

  /**
   * Inizializza il servizio di upload con le immagini esistenti del prodotto.
   * Popola FileList e ImageList con i dati dal form.
   * @returns void
   */
  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm(),
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm(),
      ValidatorsField.IMAGES,
    );
  }

  /**
   * Gestisce l'anteprima di un'immagine caricata.
   * Converte il file in base64 se necessario e mostra il modal di preview.
   * @param file File da visualizzare in anteprima
   * @returns Promise<void>
   */
  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage.set(file.url || file['preview']);
    this.previewVisible.set(true);
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? this.isValidForm.set(false)
      : this.isValidForm.set(true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };

  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLAOD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAEG FILE :IST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  /**
   * Gestisce il click del pulsante di salvataggio.
   * Determina se eseguire creazione o aggiornamento in base alla modalità CRUD.
   * @returns void
   */
  onDataSaveClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }

  /**
   * Gestisce la creazione di un nuovo prodotto.
   * Invia i dati del form al server tramite HTTP POST.
   * @returns void
   */
  onDataSubmit() {
    this.loading.set(true);
    const product = this.baseForm().getRawValue();
    if (!this.showExtraInfo()) product.extraInfos = [];
    FormUtils.removeObjectNullProperties(product);
    this.messageService.addLoadingMessage('loading');
    product.categories = [this.categories.value];
    console.log('onDataSubmit', product);
    this.productService.create(product).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('PRODUCTS.createSuccess');
        this.loading.set(false);
        this.router.navigate(['products', res.data!.id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Gestisce l'aggiornamento di un prodotto esistente.
   * Invia i dati modificati al server tramite HTTP PUT e aggiorna il breadcrumb.
   * @returns void
   */
  onDataUpdate() {
    this.loading.set(true);
    const product = this.baseForm().value;
    console.log('onDataUpdate', product);
    product.categories = [this.categories.value];
    FormUtils.removeObjectNullProperties(product);
    if (!this.showExtraInfo()) product.extraInfos = [];
    this.messageService.addLoadingMessage('loading');

    this.productService.update(this.productId(), product).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('PRODUCTS.updateSuccess');
        const breadcrumbData = res.data?.name;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Imposta la modalità CRUD (create o update) in base ai dati di routing.
   * @returns void
   */
  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  /**
   * Configura il componente per la modalità di creazione di un nuovo prodotto.
   * Resetta il form, imposta i titoli dei pulsanti e mostra il breadcrumb.
   * @returns void
   */
  override setCreateMode() {
    this.productService.setProduct(undefined);
    this.baseForm().reset();
    this.initForm();
    this.saveButtonTitle.set('PRODUCTS.createProduct');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di un prodotto esistente.
   * Carica i dati del prodotto tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.saveButtonTitle.set('PRODUCTS.updateProduct');
    this.abortButtonTitle.set('PRODUCTS.deleteProduct');

    this.productService.readOne(this.productId()).subscribe({
      next: (res) => {
        this.fillFormData();
        this.initUploadService();
        const breadcrumbData = res.data?.name;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/products');
      },
    });
  }

  /**
   * Gestisce il click del pulsante di annullamento/eliminazione.
   * In modalità create: naviga alla lista prodotti.
   * In modalità update: mostra modal di conferma per eliminazione.
   * @returns void
   */
  onAbortClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.router.navigateByUrl('/products');
        break;
      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.productService.delete(this.productId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/products');
                this.messageService.addSuccessMessage('PRODUCTS.deleteSuccess');
              },
            });
          },
          title: 'PRODUCTS.confirmDeleteTitle',
          subtitle: 'PRODUCTS.confirmDeleteSubtitle',
          cancelFn: () => this.loading.set(false),
        });
        break;
    }
  }

  /**
   * Popola il form con i dati del prodotto caricato dal server.
   * Gestisce anche la visualizzazione condizionale di link e informazioni extra.
   * @returns void
   */
  fillFormData() {
    this.baseForm().get('id')?.setValue(this.product()!.id);
    this.baseForm().get('name')?.setValue(this.product()!.name);
    this.baseForm().get('variantName')?.setValue(this.product()!.variantName);
    this.baseForm().get('categories')?.setValue(this.product()!.categories);
    this.baseForm().get('description')?.setValue(this.product()!.description);
    this.baseForm().get('images')?.setValue(this.product()!.images);
    this.baseForm().get('linkToShop')?.setValue(this.product()!.linkToShop);

    if (this.product()!.extraInfos!.length > 0) {
      this.showExtraInfo.set(true);
      this.extraInfos.clear();
      this.product()!.extraInfos!.forEach((option) => {
        this.extraInfos.push(this.newFormExtraInfo(option));
      });
    } else {
      this.showExtraInfo.set(false);
      this.extraInfos.clear();
    }

    let categories = <any>[];
    this.product()!.categories.forEach((category: ICategory) => {
      categories.push(category.id);
    });
    const category = this.categoriesList().find(
      (c) => c.id === this.product()!.categories[0].id,
    );
    this.categories.setValue(category.id);
  }

  openNewCategorModal(): void {
    this.modalService.customModal({
      title: 'Crea nuova categoria',
      content: CategoryCreateUpdateComponent,
      footer: this.tplCategoryModalFooter(),
      confirmFn: () => {},
      closable: true,
      height: '100dvh',
      maskClosable: true,
      fullScreen: true,
      className: 'custom-modal',
      componentData: {
        crudMode: crudActionType.create,
      },
    });
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
  }

  // TEST ONLY
  // onExampleDataClick() {
  //   this.baseForm.get('name')?.setValue(PRODUCT_EXAMPLE_POLO.name);
  //   this.baseForm
  //     .get('variantName')
  //     ?.setValue(PRODUCT_EXAMPLE_POLO.variantName);
  //   this.baseForm.get('isOnline')?.setValue(PRODUCT_EXAMPLE_POLO.isOnline);
  //   this.baseForm
  //     .get('description')
  //     ?.setValue(PRODUCT_EXAMPLE_POLO.description);

  //   if (PRODUCT_EXAMPLE_POLO.color) {
  //     this.showColor = true;
  //     this.baseForm
  //       .get('color.name')
  //       ?.setValue(PRODUCT_EXAMPLE_POLO.color?.name);
  //     this.baseForm.get('color.hex')?.setValue(PRODUCT_EXAMPLE_POLO.color?.hex);
  //   }

  //   this.options.clear();
  //   PRODUCT_EXAMPLE_POLO.options.forEach((option) => {
  //     this.options.push(this.newFormOption(option));
  //   });
  //   this.calculateFinalPrices();

  //   if (PRODUCT_EXAMPLE_POLO.color) {
  //     this.showColor = true;
  //     this.color.get('name')!.setValue(PRODUCT_EXAMPLE_POLO.color.name);
  //     this.color.get('hex')!.setValue(PRODUCT_EXAMPLE_POLO.color.hex);
  //   }
  // }
}
