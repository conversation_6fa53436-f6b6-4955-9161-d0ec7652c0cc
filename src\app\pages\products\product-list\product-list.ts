import { httpResource } from '@angular/common/http';
import {
  ChangeDetectorRef,
  Component,
  inject,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { EllipsisDirective } from '@core/directives/ellipsis';
import { DateFormatPipe } from '@core/pipes/date.pipe';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { ProductsService } from '@core/services/http/products';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { log } from '@core/utils/logger';
import { environment } from '@env/environment';
import { currentSectionType } from '@models/enums/current-section';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { IImage } from '@models/interfaces/image';
import { IProduct } from '@models/interfaces/product';
import { TranslateModule } from '@ngx-translate/core';
import { TableComponent } from '@shared/table/table';
import { TableQueryService } from '@shared/table/table-query.service';
import { ITableRowAction } from '@shared/table/types/table.action';
import { ITableColumn } from '@shared/table/types/table.column';
import {
  IRequestFilter,
  IRequestMeta,
  ITableQuery,
} from '@shared/table/types/table.query';
import { IRefreshData } from '@shared/table/types/table.refresh-data';
import { ITableSetting } from '@shared/table/types/table.settings';
import { TagBooleanStatusComponent } from '@shared/tags/tag-boolean-status/tag-boolean-status';
import {
  NzAvatarComponent,
  NzAvatarGroupComponent,
} from 'ng-zorro-antd/avatar';
import { NzImageModule, NzImageService } from 'ng-zorro-antd/image';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import { NzTypographyModule } from 'ng-zorro-antd/typography';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    NzAvatarComponent,
    NzAvatarGroupComponent,
    NzTooltipDirective,
    TranslateModule,
    NzImageModule,
    NullValuePipe,
    DateFormatPipe,
    EllipsisDirective,
    TagBooleanStatusComponent,
    TableComponent,
    NzTypographyModule,
  ],
  providers: [TableQueryService],
  templateUrl: './product-list.html',
  styleUrl: './product-list.less',
})
export class ProductListComponent {
  private cdr = inject(ChangeDetectorRef);

  // SERVICES
  private productService = inject(ProductsService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private messageService = inject(MessageService);
  private imageService = inject(NzImageService);

  readonly tplId = viewChild<TemplateRef<any>>('tplId');
  readonly tplIsOnline = viewChild<TemplateRef<any>>('tplIsOnline');
  readonly tplName = viewChild<TemplateRef<any>>('tplName');
  readonly tplImage = viewChild<TemplateRef<any>>('tplImage');
  readonly loading = signal<boolean>(false);
  readonly data = signal<any[]>([]);
  readonly dataQuery = signal<any>(null);
  readonly tableLayoutSettings = signal<ITableSetting>(<ITableSetting>{});
  readonly tableQueryRequest = signal<ITableQuery | null>(null);
  readonly refreshData = signal<IRefreshData>({
    interval: 0,
    min: 0,
    max: 180,
    step: 30,
  });

  constructor() {
    this.tableLayoutSettings.set({
      singleRowActions: this.buildRowActions(),
      listOfColumns: this.buildTableColumns(),
      dynamicTableHeightOffset: 375,
      pagination: {
        total: 0,
        pageSize: 50,
        pageIndex: 1,
      },
    });

    // Initialize with empty data to ensure table renders properly
    this.data.set([]);
    this.headerService.setCurrentSection(currentSectionType.productList);
  }

  private _baseProductsApi = `${environment.api.products}`;

  findAll = (meta: IRequestMeta, filter: IRequestFilter[]) => {
    return httpResource<IBaseResponse<IProduct[], IFindResponseMeta>>(
      () => ({
        url: `${this._baseProductsApi}/search`,
        method: 'POST',
        body: {
          meta,
          filter,
        },
      }),
      {
        defaultValue: {
          data: [],
          meta: {
            page: {
              total: 0,
              pageIndex: 0,
              pageSize: 0,
            },
          },
        } as IBaseResponse<IProduct[], IFindResponseMeta>,
      },
    );
  };

  onQueryChange(query: ITableQuery) {
    this.tableQueryRequest.set(query);
    this.loading.set(true);

    this.productService.search(query.meta, query.filter).subscribe((value) => {
      this.data.set(value.data);
      const currentSettings = this.tableLayoutSettings();
      currentSettings.pagination.total = value.meta!.page.total;
      currentSettings.pagination.pageIndex = value.meta!.page.pageIndex;
      currentSettings.pagination.pageSize = value.meta!.page.pageSize;
      this.tableLayoutSettings.set({ ...currentSettings });
      this.loading.set(false);
      this.cdr.detectChanges();
    });
    this.cdr.detectChanges();
  }

  ngAfterViewInit(): void {
    this.tableLayoutSettings().listOfColumns.forEach((col) => {
      if (col.cellTemplateName) {
        const templateRef = this[col.cellTemplateName];
        if (templateRef && templateRef()) {
          col.cellTemplate = templateRef();
        }
      }
    });
  }

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.modify',
        icon: 'edit',
        callbackFn: (row: IProduct) => {
          this.router.navigate([row.id], { relativeTo: this.route });
        },
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        callbackFn: (row: IProduct) => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.productService.delete(row.id).subscribe({
                next: () => {
                  this.onQueryChange(this.tableQueryRequest()!);
                  this.messageService.addSuccessMessage(
                    'PRODUCTS.deleteSuccess',
                  );
                  log(`PRODUCT ID: ${row.id} - Eliminato`);
                },
              });
            },
            title: 'PRODUCTS.confirmDeleteTitle',
            subtitle: 'PRODUCTS.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  private buildTableColumns(): ITableColumn[] {
    return [
      {
        id: 'id',
        cellTemplate: null,
        cellTemplateName: 'tplId',
        title: 'ID',
        sortOrder: null,
        sortFn: true,
        width: '60px',
        lockOnLeft: true,
        visible: true,
      },
      {
        id: 'images',
        title: 'PRODUCTS.images',
        cellTemplate: null,
        cellTemplateName: 'tplImage',
        width: '50px',
        visible: true,
      },
      {
        id: 'name',
        title: 'PRODUCTS.name',
        cellTemplate: null,
        cellTemplateName: 'tplName',
        sortOrder: null,
        sortFn: true,
        width: '80px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'isOnline',
        title: 'PRODUCTS.isOnline',
        cellTemplate: null,
        cellTemplateName: 'tplIsOnline',
        sortOrder: null,
        sortFn: true,
        width: '50px',
        visible: true,
        filterFn: true,
        listOfFilter: [
          {
            text: 'PRODUCTS.no',
            value: false,
          },
          {
            text: 'PRODUCTS.yes',
            value: true,
          },
        ],
      },
    ];
  }

  goToDetail(productId: string) {
    this.router.navigate([productId], { relativeTo: this.route });
  }

  onImageClick(images: IImage[]): void {
    const imagePreview = images.map((image) => {
      return { src: image.url, alt: image.name };
    });
    this.imageService.preview(imagePreview, { nzZoom: 1, nzRotate: 0 });
  }
}
